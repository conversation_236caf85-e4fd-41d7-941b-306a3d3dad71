{"name": "automotive-ui", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.21", "framer-motion": "^12.22.0", "lucide-react": "^0.525.0", "postcss": "^8.5.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.3", "tailwindcss": "^4.1.11"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "typescript": "~5.6.2", "vite": "^6.0.3"}}