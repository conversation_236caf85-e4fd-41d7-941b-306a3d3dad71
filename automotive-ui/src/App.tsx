import React, { useState } from 'react';
import { VehicleStatusBar } from './components/VehicleStatusBar';
import { BottomDock, ViewType } from './components/BottomDock';
import { NavigationView } from './components/NavigationView';
import { MediaView } from './components/MediaView';
import { ClimateView } from './components/ClimateView';
import { SettingsView } from './components/SettingsView';
import { CarModel3D } from './components/CarModel3D';

function App() {
  const [currentView, setCurrentView] = useState<ViewType>('navigation');
  const [showCarModel, setShowCarModel] = useState(true);

  const renderMainContent = () => {
    // Default view - Tesla-like layout with car on left, navigation on right
    if (currentView === 'navigation' && showCarModel) {
      return (
        <div className="flex h-full">
          {/* Left side - Car Model and Speed */}
          <div className="flex-1 bg-gradient-to-br from-gray-900 to-gray-800 relative overflow-hidden">
            {/* Speed Display - Top Left */}
            <div className="absolute top-12 left-12 z-10">
              <div className="text-white">
                <div className="text-8xl font-thin tracking-tight mb-3">65</div>
                <div className="text-2xl font-light opacity-70">mph</div>
              </div>
            </div>

            {/* Car Model - Center */}
            <div className="absolute inset-0 flex items-center justify-center">
              <CarModel3D />
            </div>

            {/* Vehicle Stats - Bottom Left */}
            <div className="absolute bottom-12 left-12 space-y-6">
              <div className="text-white/80">
                <div className="text-sm opacity-60 mb-1">Range</div>
                <div className="text-3xl font-light">287 mi</div>
              </div>
              <div className="text-white/80">
                <div className="text-sm opacity-60 mb-1">Fuel</div>
                <div className="text-3xl font-light">68%</div>
              </div>
            </div>
          </div>

          {/* Right side - Navigation */}
          <div className="w-[480px] bg-white border-l border-gray-200">
            <NavigationView />
          </div>
        </div>
      );
    }

    // Other views
    switch (currentView) {
      case 'navigation':
        return <NavigationView />;
      case 'media':
        return <MediaView />;
      case 'climate':
        return <ClimateView />;
      case 'settings':
        return <SettingsView />;
      default:
        return <NavigationView />;
    }
  };

  return (
    <div className="h-screen w-screen bg-black flex flex-col overflow-hidden">
      {/* Status Bar */}
      <VehicleStatusBar />
      
      {/* Main Content */}
      <div className="flex-1 relative">
        {renderMainContent()}
      </div>
      
      {/* Bottom Dock */}
      <div className="bg-white/95 backdrop-blur-xl border-t border-gray-200/50 px-8 py-6 mb-4 mx-4 rounded-2xl">
        <BottomDock
          currentView={currentView}
          onViewChange={setCurrentView}
          onToggleCarModel={() => setShowCarModel(!showCarModel)}
          showCarModel={showCarModel}
        />
      </div>
    </div>
  );
}

export default App;
