import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { VehicleStatusBar } from './components/VehicleStatusBar';
import { BottomDock, ViewType } from './components/BottomDock';
import { NavigationView } from './components/NavigationView';
import { MediaView } from './components/MediaView';
import { ClimateView } from './components/ClimateView';
import { SettingsView } from './components/SettingsView';
import { CarModel3D } from './components/CarModel3D';

// Animation variants
const pageVariants = {
  initial: { opacity: 0, x: 20 },
  in: { opacity: 1, x: 0 },
  out: { opacity: 0, x: -20 }
};

const pageTransition = {
  type: "tween",
  ease: "anticipate",
  duration: 0.4
};

const carModelVariants = {
  hidden: { opacity: 0, scale: 0.8, rotateY: -15 },
  visible: {
    opacity: 1,
    scale: 1,
    rotateY: 0,
    transition: {
      duration: 0.8,
      ease: "easeOut"
    }
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    rotateY: 15,
    transition: {
      duration: 0.5,
      ease: "easeIn"
    }
  }
};

function App() {
  const [currentView, setCurrentView] = useState<ViewType>('navigation');
  const [showCarModel, setShowCarModel] = useState(true);

  const renderMainContent = () => {
    // Default view - Tesla-like layout with car on left, navigation on right
    if (currentView === 'navigation' && showCarModel) {
      return (
        <motion.div
          className="flex h-full flex-col lg:flex-row"
          initial="initial"
          animate="in"
          exit="out"
          variants={pageVariants}
          transition={pageTransition}
        >
          {/* Left side - Car Model and Speed */}
          <motion.div
            className="flex-1 bg-gradient-to-br from-neutral-900 to-neutral-800 relative overflow-hidden min-h-[50vh] lg:min-h-full"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            {/* Speed Display - Top Left */}
            <motion.div
              className="absolute top-6 left-6 lg:top-12 lg:left-12 z-10"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              <div className="text-white">
                <motion.div
                  className="text-6xl lg:text-8xl xl:text-9xl font-thin tracking-tight mb-2 lg:mb-3"
                  initial={{ scale: 0.8 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.4, duration: 0.6, type: "spring", stiffness: 100 }}
                >
                  65
                </motion.div>
                <div className="text-lg lg:text-2xl font-light opacity-70">mph</div>
              </div>
            </motion.div>

            {/* Car Model - Center */}
            <AnimatePresence>
              <motion.div
                className="absolute inset-0 flex items-center justify-center"
                variants={carModelVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
              >
                <div className="transform scale-75 lg:scale-100">
                  <CarModel3D />
                </div>
              </motion.div>
            </AnimatePresence>

            {/* Vehicle Stats - Bottom Left */}
            <motion.div
              className="absolute bottom-6 left-6 lg:bottom-12 lg:left-12 space-y-4 lg:space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
            >
              <motion.div
                className="glass-dark p-4 rounded-2xl hover-lift"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <div className="text-white/60 text-xs lg:text-sm uppercase tracking-wide mb-1 font-medium">Range</div>
                <div className="text-2xl lg:text-3xl font-light text-white">287 mi</div>
              </motion.div>
              <motion.div
                className="glass-dark p-4 rounded-2xl hover-lift"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <div className="text-white/60 text-xs lg:text-sm uppercase tracking-wide mb-1 font-medium">Fuel</div>
                <div className="text-2xl lg:text-3xl font-light text-white">68%</div>
              </motion.div>
            </motion.div>

            {/* Mobile overlay for better touch interaction */}
            <div className="lg:hidden absolute inset-0 bg-black/20 pointer-events-none" />
          </motion.div>

          {/* Right side - Navigation */}
          <motion.div
            className="w-full lg:w-[520px] xl:w-[600px] bg-white border-t lg:border-t-0 lg:border-l border-neutral-200 flex-shrink-0"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3, duration: 0.6, ease: "easeOut" }}
          >
            <NavigationView />
          </motion.div>
        </motion.div>
      );
    }

    // Other views with smooth transitions
    const ViewComponent = () => {
      switch (currentView) {
        case 'navigation':
          return <NavigationView />;
        case 'media':
          return <MediaView />;
        case 'climate':
          return <ClimateView />;
        case 'settings':
          return <SettingsView />;
        default:
          return <NavigationView />;
      }
    };

    return (
      <AnimatePresence mode="wait">
        <motion.div
          key={currentView}
          initial="initial"
          animate="in"
          exit="out"
          variants={pageVariants}
          transition={pageTransition}
          className="h-full"
        >
          <ViewComponent />
        </motion.div>
      </AnimatePresence>
    );
  };

  return (
    <motion.div
      className="h-screen w-screen bg-black flex flex-col overflow-hidden safe-area-inset"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Status Bar */}
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.1, duration: 0.5 }}
      >
        <VehicleStatusBar />
      </motion.div>

      {/* Main Content */}
      <div className="flex-1 relative min-h-0">
        <AnimatePresence mode="wait">
          {renderMainContent()}
        </AnimatePresence>
      </div>

      {/* Bottom Dock */}
      <motion.div
        className="dock mx-4 mb-4 lg:mx-6 lg:mb-6"
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.8, duration: 0.6, type: "spring", stiffness: 100 }}
      >
        <BottomDock
          currentView={currentView}
          onViewChange={setCurrentView}
          onToggleCarModel={() => setShowCarModel(!showCarModel)}
          showCarModel={showCarModel}
        />
      </motion.div>
    </motion.div>
  );
}

export default App;
