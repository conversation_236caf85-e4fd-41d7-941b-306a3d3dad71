@import "tailwindcss";

@theme {
  --color-tesla-blue: #3B82F6;
  --color-tesla-darkBlue: #1E40AF;
  --color-tesla-gray: #F3F4F6;
  --color-tesla-darkGray: #374151;
  --color-tesla-black: #111827;
  --font-tesla: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  html {
    height: 100%;
  }
  
  body {
    @apply bg-gray-50 font-tesla antialiased;
    height: 100%;
    font-family: var(--font-tesla);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  #root {
    height: 100%;
  }
}

@layer components {
  .btn-tesla {
    @apply px-6 py-3 bg-tesla-blue text-white rounded-lg font-medium hover:bg-tesla-darkBlue active:scale-95 transition-all duration-200 shadow-sm;
  }
  
  .btn-tesla-secondary {
    @apply px-6 py-3 bg-white text-tesla-darkGray border border-gray-200 rounded-lg font-medium hover:bg-gray-50 active:scale-95 transition-all duration-200 shadow-sm;
  }
  
  .card-tesla {
    @apply bg-white rounded-2xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300;
  }
  
  .dock-tesla {
    @apply bg-white/80 backdrop-blur-lg rounded-2xl shadow-lg border border-white/20 p-4;
  }
  
  .input-tesla {
    @apply px-4 py-3 bg-white border border-gray-200 rounded-xl focus:ring-2 focus:ring-tesla-blue focus:border-tesla-blue transition-all duration-200 placeholder:text-gray-400;
  }
  
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
}

@layer utilities {
  /* Glass morphism effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  /* Tesla hover effects */
  .hover-tesla {
    @apply hover:bg-tesla-blue hover:text-white transition-all duration-300 cursor-pointer;
  }
  
  /* Responsive text sizing */
  .text-responsive-sm {
    @apply text-sm lg:text-base;
  }
  
  .text-responsive-lg {
    @apply text-lg lg:text-xl xl:text-2xl;
  }
}

.logo.vite:hover {
  filter: drop-shadow(0 0 2em #747bff);
}

.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafb);
}
:root {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;

  color: #0f0f0f;
  background-color: #f6f6f6;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

.container {
  margin: 0;
  padding-top: 10vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: 0.75s;
}

.logo.tauri:hover {
  filter: drop-shadow(0 0 2em #24c8db);
}

.row {
  display: flex;
  justify-content: center;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}

a:hover {
  color: #535bf2;
}

h1 {
  text-align: center;
}

input,
button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  color: #0f0f0f;
  background-color: #ffffff;
  transition: border-color 0.25s;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
}

button {
  cursor: pointer;
}

button:hover {
  border-color: #396cd8;
}
button:active {
  border-color: #396cd8;
  background-color: #e8e8e8;
}

input,
button {
  outline: none;
}

#greet-input {
  margin-right: 5px;
}

@media (prefers-color-scheme: dark) {
  :root {
    color: #f6f6f6;
    background-color: #2f2f2f;
  }

  a:hover {
    color: #24c8db;
  }

  input,
  button {
    color: #ffffff;
    background-color: #0f0f0f98;
  }
  button:active {
    background-color: #0f0f0f69;
  }
}
