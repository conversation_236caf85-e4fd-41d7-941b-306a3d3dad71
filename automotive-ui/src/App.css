@import "tailwindcss";

@theme {
  /* Primary Brand Colors */
  --color-primary: #2563EB;
  --color-primary-dark: #1D4ED8;
  --color-primary-light: #3B82F6;
  --color-primary-50: #EFF6FF;
  --color-primary-100: #DBEAFE;
  --color-primary-500: #3B82F6;
  --color-primary-600: #2563EB;
  --color-primary-700: #1D4ED8;
  --color-primary-900: #1E3A8A;

  /* Neutral Colors */
  --color-neutral-50: #F8FAFC;
  --color-neutral-100: #F1F5F9;
  --color-neutral-200: #E2E8F0;
  --color-neutral-300: #CBD5E1;
  --color-neutral-400: #94A3B8;
  --color-neutral-500: #64748B;
  --color-neutral-600: #475569;
  --color-neutral-700: #334155;
  --color-neutral-800: #1E293B;
  --color-neutral-900: #0F172A;
  --color-neutral-950: #020617;

  /* Status Colors */
  --color-success: #10B981;
  --color-success-light: #34D399;
  --color-success-dark: #059669;
  --color-warning: #F59E0B;
  --color-warning-light: #FBBF24;
  --color-warning-dark: #D97706;
  --color-error: #EF4444;
  --color-error-light: #F87171;
  --color-error-dark: #DC2626;

  /* Glass Morphism */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  /* Typography */
  --font-primary: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-mono: "JetBrains Mono", "Fira Code", Consolas, monospace;

  /* Spacing Scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --radius-2xl: 2rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 500ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    height: 100%;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-neutral-50 antialiased;
    height: 100%;
    font-family: var(--font-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-tap-highlight-color: transparent;
    overflow: hidden; /* Prevent scrolling in automotive context */
  }

  #root {
    height: 100%;
  }

  /* Focus styles for accessibility */
  *:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }

  /* Smooth transitions for all interactive elements */
  button, input, select, textarea {
    transition: all var(--transition-fast);
  }
}

@layer components {
  /* Button Components */
  .btn-primary {
    @apply px-6 py-3 bg-primary text-white rounded-xl font-semibold
           hover:bg-primary-dark active:scale-95
           transition-all duration-200 shadow-md hover:shadow-lg
           focus:ring-4 focus:ring-primary/20 focus:outline-none
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply px-6 py-3 bg-white text-neutral-700 border border-neutral-200
           rounded-xl font-semibold hover:bg-neutral-50 active:scale-95
           transition-all duration-200 shadow-sm hover:shadow-md
           focus:ring-4 focus:ring-primary/20 focus:outline-none;
  }

  .btn-ghost {
    @apply px-6 py-3 text-neutral-700 rounded-xl font-semibold
           hover:bg-neutral-100 active:scale-95
           transition-all duration-200
           focus:ring-4 focus:ring-primary/20 focus:outline-none;
  }

  .btn-icon {
    @apply p-3 rounded-xl transition-all duration-200
           hover:bg-neutral-100 active:scale-95
           focus:ring-4 focus:ring-primary/20 focus:outline-none;
  }

  /* Card Components */
  .card {
    @apply bg-white rounded-2xl shadow-sm border border-neutral-200
           hover:shadow-md transition-all duration-300;
  }

  .card-elevated {
    @apply bg-white rounded-2xl shadow-lg border border-neutral-100
           hover:shadow-xl transition-all duration-300;
  }

  .card-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    @apply rounded-2xl;
  }

  /* Input Components */
  .input-primary {
    @apply px-4 py-3 bg-white border border-neutral-200 rounded-xl
           focus:ring-4 focus:ring-primary/20 focus:border-primary
           transition-all duration-200 placeholder:text-neutral-400
           hover:border-neutral-300;
  }

  .input-large {
    @apply px-6 py-4 text-lg bg-white border-2 border-neutral-200 rounded-2xl
           focus:ring-4 focus:ring-primary/20 focus:border-primary
           transition-all duration-200 placeholder:text-neutral-400
           hover:border-neutral-300;
  }

  /* Glass Morphism */
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  }

  /* Status Indicators */
  .status-success {
    @apply bg-success text-white;
  }

  .status-warning {
    @apply bg-warning text-white;
  }

  .status-error {
    @apply bg-error text-white;
  }

  /* Dock Component */
  .dock {
    @apply bg-white/95 backdrop-blur-xl border border-neutral-200/50
           rounded-2xl shadow-xl p-6;
  }
}

@layer utilities {
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn var(--transition-normal) ease-out;
  }

  .animate-slide-up {
    animation: slideUp var(--transition-normal) ease-out;
  }

  .animate-scale-in {
    animation: scaleIn var(--transition-normal) ease-out;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Hover effects */
  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1 hover:shadow-lg;
  }

  .hover-scale {
    @apply transition-transform duration-200 hover:scale-105;
  }

  .hover-glow {
    @apply transition-all duration-300;
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  /* Touch-friendly utilities */
  .touch-target {
    @apply min-w-[44px] min-h-[44px];
  }

  .touch-target-large {
    @apply min-w-[60px] min-h-[60px];
  }

  /* Responsive text sizing */
  .text-responsive-sm {
    @apply text-sm lg:text-base;
  }

  .text-responsive-md {
    @apply text-base lg:text-lg xl:text-xl;
  }

  .text-responsive-lg {
    @apply text-lg lg:text-xl xl:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl lg:text-2xl xl:text-3xl;
  }

  /* Layout utilities */
  .safe-area-inset {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Scrollbar styling */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: var(--color-neutral-300) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: var(--color-neutral-300);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: var(--color-neutral-400);
  }
}

/* Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Loading skeleton animation */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Media Queries for Responsive Design */
@media (max-width: 768px) {
  .dock {
    @apply p-4;
  }

  .btn-primary, .btn-secondary {
    @apply px-4 py-2 text-sm;
  }
}

@media (max-width: 480px) {
  .card {
    @apply rounded-xl;
  }

  .input-large {
    @apply px-4 py-3 text-base;
  }
}
