import React from 'react';
import {
  Navigation,
  Play,
  Thermometer,
  Settings,
  Car,
  ChevronUp
} from 'lucide-react';

export type ViewType = 'navigation' | 'media' | 'climate' | 'settings';

interface BottomDockProps {
  currentView: ViewType;
  onViewChange: (view: ViewType) => void;
  onToggleCarModel: () => void;
  showCarModel: boolean;
}

interface DockButtonProps {
  icon: React.ReactNode;
  label: string;
  isActive: boolean;
  onClick: () => void;
  isPrimary?: boolean;
}



const DockButton: React.FC<DockButtonProps> = ({ icon, label, isActive, onClick, isPrimary = false }) => {
  return (
    <button
      onClick={onClick}
      className={`relative flex flex-col items-center justify-center transition-all duration-300 rounded-2xl group touch-target ${
        isPrimary
          ? 'px-8 py-5 min-w-[140px]'
          : 'p-5 min-w-[90px]'
      } ${
        isActive
          ? isPrimary
            ? 'bg-primary text-white shadow-xl shadow-primary/25 scale-105 hover-glow'
            : 'bg-neutral-900 text-white shadow-lg shadow-neutral-900/25 scale-105'
          : 'text-neutral-600 hover:text-neutral-900 hover:bg-neutral-100 active:scale-95 hover-lift'
      }`}
    >
      {/* Background glow effect for active state */}
      {isActive && (
        <div className={`absolute inset-0 rounded-2xl ${
          isPrimary ? 'bg-primary' : 'bg-neutral-900'
        } opacity-10 blur-xl scale-110 transition-all duration-500`} />
      )}

      <div className={`relative transition-all duration-300 group-hover:scale-110 ${
        isPrimary ? 'w-8 h-8 mb-3' : 'w-7 h-7 mb-2'
      } ${isActive ? 'animate-scale-in' : ''}`}>
        {icon}
      </div>

      <span className={`relative font-semibold tracking-tight transition-all duration-300 ${
        isPrimary ? 'text-sm' : 'text-xs'
      } ${isActive ? 'animate-fade-in' : ''}`}>
        {label}
      </span>

      {/* Active indicator */}
      {isActive && !isPrimary && (
        <div className="absolute -bottom-2 w-8 h-1 bg-neutral-900 rounded-full transition-all duration-300 animate-scale-in" />
      )}

      {/* Hover indicator */}
      <div className={`absolute inset-0 rounded-2xl border-2 border-transparent transition-all duration-300 ${
        !isActive ? 'group-hover:border-neutral-200' : ''
      }`} />
    </button>
  );
};

export const BottomDock: React.FC<BottomDockProps> = ({
  currentView,
  onViewChange,
  onToggleCarModel,
  showCarModel
}) => {
  const dockItems = [
    {
      id: 'navigation' as ViewType,
      icon: <Navigation className="w-full h-full" strokeWidth={2.5} />,
      label: 'Navigate'
    },
    {
      id: 'media' as ViewType,
      icon: <Play className="w-full h-full" strokeWidth={2.5} />,
      label: 'Media'
    },
    {
      id: 'climate' as ViewType,
      icon: <Thermometer className="w-full h-full" strokeWidth={2.5} />,
      label: 'Climate'
    },
    {
      id: 'settings' as ViewType,
      icon: <Settings className="w-full h-full" strokeWidth={2.5} />,
      label: 'Settings'
    }
  ];

  return (
    <div className="flex items-center justify-between w-full max-w-5xl mx-auto animate-slide-up">
      {/* Main Navigation Buttons */}
      <div className="flex items-center space-x-4">
        {dockItems.map((item, index) => (
          <div
            key={item.id}
            className="animate-fade-in"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <DockButton
              icon={item.icon}
              label={item.label}
              isActive={currentView === item.id && !showCarModel}
              onClick={() => onViewChange(item.id)}
            />
          </div>
        ))}
      </div>

      {/* Right side - Car Model Toggle */}
      <div className="animate-fade-in" style={{ animationDelay: '400ms' }}>
        <DockButton
          icon={
            <div className="relative">
              <Car className="w-full h-full" strokeWidth={2.5} />
              {showCarModel && (
                <ChevronUp className="absolute -top-1 -right-1 w-4 h-4 text-white bg-primary rounded-full p-0.5" strokeWidth={3} />
              )}
            </div>
          }
          label="Vehicle"
          isActive={showCarModel}
          onClick={onToggleCarModel}
          isPrimary={true}
        />
      </div>
    </div>
  );
}; 