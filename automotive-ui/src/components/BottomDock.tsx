import React from 'react';

export type ViewType = 'navigation' | 'media' | 'climate' | 'settings';

interface BottomDockProps {
  currentView: ViewType;
  onViewChange: (view: ViewType) => void;
  onToggleCarModel: () => void;
  showCarModel: boolean;
}

interface DockButtonProps {
  icon: React.ReactNode;
  label: string;
  isActive: boolean;
  onClick: () => void;
  isPrimary?: boolean;
}

// Modern Custom SVG Icons
const NavigationIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
    <path d="M12 2L22 12L18 12L18 20L6 20L6 12L2 12L12 2Z"/>
  </svg>
);

const MediaIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
    <circle cx="12" cy="12" r="10"/>
    <polygon points="10,8 16,12 10,16"/>
  </svg>
);

const ClimateIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
    <path d="M14 4V10C15.1 10.9 16 12.4 16 14C16 16.2 14.2 18 12 18S8 16.2 8 14C8 12.4 8.9 10.9 10 10V4C10 2.9 10.9 2 12 2S14 2.9 14 4Z"/>
    <circle cx="12" cy="14" r="3"/>
  </svg>
);

const SettingsIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
    <circle cx="12" cy="12" r="3"/>
    <path d="M12 1V3M12 21V23M4.22 4.22L5.64 5.64M18.36 18.36L19.78 19.78M1 12H3M21 12H23M4.22 19.78L5.64 18.36M18.36 5.64L19.78 4.22"/>
  </svg>
);

const CarModelIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
    <path d="M5 17H4a2 2 0 01-2-2v-5a2 2 0 012-2h16a2 2 0 012 2v5a2 2 0 01-2 2h-1"/>
    <polygon points="7,8 9,4 15,4 17,8"/>
    <circle cx="6.5" cy="17" r="2.5"/>
    <circle cx="17.5" cy="17" r="2.5"/>
  </svg>
);

const DockButton: React.FC<DockButtonProps> = ({ icon, label, isActive, onClick, isPrimary = false }) => {
  return (
    <button
      onClick={onClick}
      className={`relative flex flex-col items-center justify-center transition-all duration-300 rounded-2xl group ${
        isPrimary 
          ? 'px-6 py-4 min-w-[120px]'
          : 'p-4 min-w-[80px]'
      } ${
        isActive 
          ? isPrimary
            ? 'bg-blue-600 text-white shadow-xl shadow-blue-600/25 scale-105' 
            : 'bg-gray-900 text-white shadow-lg shadow-gray-900/25 scale-105'
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 active:scale-95'
      }`}
    >
      <div className={`transition-transform duration-200 group-hover:scale-110 ${
        isPrimary ? 'w-7 h-7 mb-2' : 'w-6 h-6 mb-2'
      }`}>
        {icon}
      </div>
      <span className={`font-medium tracking-tight ${
        isPrimary ? 'text-sm' : 'text-xs'
      }`}>
        {label}
      </span>
      
      {/* Active indicator */}
      {isActive && !isPrimary && (
        <div className="absolute -bottom-1 w-6 h-1 bg-gray-900 rounded-full transition-all duration-300" />
      )}
    </button>
  );
};

export const BottomDock: React.FC<BottomDockProps> = ({
  currentView,
  onViewChange,
  onToggleCarModel,
  showCarModel
}) => {
  const dockItems = [
    {
      id: 'navigation' as ViewType,
      icon: <NavigationIcon className="w-full h-full" />,
      label: 'Navigate'
    },
    {
      id: 'media' as ViewType,
      icon: <MediaIcon className="w-full h-full" />,
      label: 'Media'
    },
    {
      id: 'climate' as ViewType,
      icon: <ClimateIcon className="w-full h-full" />,
      label: 'Climate'
    },
    {
      id: 'settings' as ViewType,
      icon: <SettingsIcon className="w-full h-full" />,
      label: 'Settings'
    }
  ];

  return (
    <div className="flex items-center justify-between w-full max-w-4xl mx-auto">
      {/* Main Navigation Buttons */}
      <div className="flex items-center space-x-3">
        {dockItems.map((item) => (
          <DockButton
            key={item.id}
            icon={item.icon}
            label={item.label}
            isActive={currentView === item.id && !showCarModel}
            onClick={() => onViewChange(item.id)}
          />
        ))}
      </div>

      {/* Right side - Car Model Toggle */}
      <DockButton
        icon={<CarModelIcon className="w-full h-full" />}
        label="Vehicle"
        isActive={showCarModel}
        onClick={onToggleCarModel}
        isPrimary={true}
      />
    </div>
  );
}; 