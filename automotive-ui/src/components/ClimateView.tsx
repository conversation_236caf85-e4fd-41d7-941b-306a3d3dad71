import React, { useState } from 'react';

// Custom SVG Icons
const ThermometerIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M14 13V5c0-1.1-.9-2-2-2s-2 .9-2 2v8c-1.21.91-2 2.37-2 4 0 2.76 2.24 5 5 5s5-2.24 5-5c0-1.63-.79-3.09-2-4zM12 19c-1.1 0-2-.9-2-2 0-.78.99-2 2-2s2 1.22 2 2c0 1.1-.9 2-2 2z"/>
    <circle cx="12" cy="9" r="1"/>
  </svg>
);

const FanIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2L13.09 8.26L22 9.27L17.36 13.36L18.82 21.02L12 17.77L5.18 21.02L6.64 13.36L2 9.27L10.91 8.26L12 2Z"/>
  </svg>
);

const AirConditioningIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
  </svg>
);

const HeaterIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
  </svg>
);

const AutoIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 5-5v10zm8-5l-5 5V7l5 5z"/>
  </svg>
);

const MaxAcIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-8 14l-6-6h4v-6h4v6h4l-6 6z"/>
  </svg>
);

const DefrostIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2L11 3V8L9 6L7 8L12 13L17 8L15 6L13 8V3L12 2ZM5 15L7 13L9 15L7 17L5 15ZM12 15L14 13L16 15L14 17L12 15ZM19 15L21 13L23 15L21 17L19 15ZM5 18H23V20H5V18Z"/>
  </svg>
);

const SeatHeaterIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M22 2H2C1.45 2 1 2.45 1 3V21C1 21.55 1.45 22 2 22H22C22.55 22 23 21.55 23 21V3C23 2.45 22.55 2 22 2ZM21 20H3V4H21V20ZM6 16H18V14H6V16ZM6 12H18V10H6V12ZM6 8H18V6H6V8Z"/>
  </svg>
);

const RecirculationIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
  </svg>
);

const PowerIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M13 3h-2v10h2V3zm4.83 2.17l-1.42 1.42C17.99 7.86 19 9.81 19 12c0 3.87-3.13 7-7 7s-7-3.13-7-7c0-2.19 1.01-4.14 2.58-5.42L6.17 5.17C4.23 6.82 3 9.26 3 12c0 4.97 4.03 9 9 9s9-4.03 9-9c0-2.74-1.23-5.18-3.17-6.83z"/>
  </svg>
);

export const ClimateView: React.FC = () => {
  const [leftTemp, setLeftTemp] = useState(72);
  const [rightTemp, setRightTemp] = useState(72);
  const [fanSpeed, setFanSpeed] = useState(3);
  const [isAcOn, setIsAcOn] = useState(true);
  const [isHeaterOn, setIsHeaterOn] = useState(false);
  const [isAutoMode, setIsAutoMode] = useState(true);
  const [isRecirculating, setIsRecirculating] = useState(false);
  const [seatHeaterLeft, setSeatHeaterLeft] = useState(0);
  const [seatHeaterRight, setSeatHeaterRight] = useState(0);

  const adjustTemp = (side: 'left' | 'right', delta: number) => {
    if (side === 'left') {
      setLeftTemp(Math.max(60, Math.min(85, leftTemp + delta)));
    } else {
      setRightTemp(Math.max(60, Math.min(85, rightTemp + delta)));
    }
  };

  const getSeatHeaterColor = (level: number) => {
    if (level === 0) return 'text-gray-400';
    if (level === 1) return 'text-yellow-500';
    if (level === 2) return 'text-orange-500';
    return 'text-red-500';
  };

  return (
    <div className="h-full bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-semibold text-tesla-darkGray mb-2">Climate Control</h1>
          <p className="text-gray-600">Dual-zone temperature control system</p>
        </div>

        {/* Main Temperature Controls */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Zone */}
          <div className="card-tesla">
            <div className="text-center mb-6">
              <div className="text-sm font-medium text-gray-600 mb-2">Driver Zone</div>
              <div className="text-6xl font-light text-tesla-darkGray mb-4">{leftTemp}°</div>
              <div className="flex items-center justify-center space-x-4">
                <button
                  onClick={() => adjustTemp('left', -1)}
                  className="w-12 h-12 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
                >
                  <span className="text-2xl text-gray-600">-</span>
                </button>
                <ThermometerIcon className="w-8 h-8 text-tesla-blue" />
                <button
                  onClick={() => adjustTemp('left', 1)}
                  className="w-12 h-12 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
                >
                  <span className="text-2xl text-gray-600">+</span>
                </button>
              </div>
            </div>
            
            {/* Seat Heater */}
            <div className="text-center">
              <div className="text-sm font-medium text-gray-600 mb-3">Seat Heater</div>
              <div className="flex items-center justify-center space-x-2">
                {[0, 1, 2, 3].map((level) => (
                  <button
                    key={level}
                    onClick={() => setSeatHeaterLeft(level)}
                    className={`w-10 h-10 rounded-full border-2 flex items-center justify-center transition-all ${
                      seatHeaterLeft === level
                        ? 'border-tesla-blue bg-tesla-blue text-white'
                        : 'border-gray-300 hover:border-tesla-blue'
                    }`}
                  >
                    <SeatHeaterIcon className={`w-5 h-5 ${
                      seatHeaterLeft === level ? 'text-white' : getSeatHeaterColor(level)
                    }`} />
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Right Zone */}
          <div className="card-tesla">
            <div className="text-center mb-6">
              <div className="text-sm font-medium text-gray-600 mb-2">Passenger Zone</div>
              <div className="text-6xl font-light text-tesla-darkGray mb-4">{rightTemp}°</div>
              <div className="flex items-center justify-center space-x-4">
                <button
                  onClick={() => adjustTemp('right', -1)}
                  className="w-12 h-12 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
                >
                  <span className="text-2xl text-gray-600">-</span>
                </button>
                <ThermometerIcon className="w-8 h-8 text-tesla-blue" />
                <button
                  onClick={() => adjustTemp('right', 1)}
                  className="w-12 h-12 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
                >
                  <span className="text-2xl text-gray-600">+</span>
                </button>
              </div>
            </div>
            
            {/* Seat Heater */}
            <div className="text-center">
              <div className="text-sm font-medium text-gray-600 mb-3">Seat Heater</div>
              <div className="flex items-center justify-center space-x-2">
                {[0, 1, 2, 3].map((level) => (
                  <button
                    key={level}
                    onClick={() => setSeatHeaterRight(level)}
                    className={`w-10 h-10 rounded-full border-2 flex items-center justify-center transition-all ${
                      seatHeaterRight === level
                        ? 'border-tesla-blue bg-tesla-blue text-white'
                        : 'border-gray-300 hover:border-tesla-blue'
                    }`}
                  >
                    <SeatHeaterIcon className={`w-5 h-5 ${
                      seatHeaterRight === level ? 'text-white' : getSeatHeaterColor(level)
                    }`} />
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Fan Speed Control */}
        <div className="card-tesla">
          <div className="text-center mb-6">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <FanIcon className="w-8 h-8 text-tesla-blue" />
              <h3 className="text-xl font-semibold text-tesla-darkGray">Fan Speed</h3>
            </div>
            <div className="text-3xl font-light text-tesla-darkGray mb-4">{fanSpeed}</div>
          </div>
          <div className="flex items-center justify-center space-x-4">
            <button
              onClick={() => setFanSpeed(Math.max(0, fanSpeed - 1))}
              className="w-12 h-12 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
            >
              <span className="text-2xl text-gray-600">-</span>
            </button>
            <div className="flex-1 mx-8">
              <input
                type="range"
                min="0"
                max="10"
                value={fanSpeed}
                onChange={(e) => setFanSpeed(Number(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              />
            </div>
            <button
              onClick={() => setFanSpeed(Math.min(10, fanSpeed + 1))}
              className="w-12 h-12 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
            >
              <span className="text-2xl text-gray-600">+</span>
            </button>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={() => setIsAutoMode(!isAutoMode)}
            className={`card-tesla text-center p-6 transition-all ${
              isAutoMode ? 'bg-tesla-blue text-white' : 'hover:bg-gray-50'
            }`}
          >
            <AutoIcon className={`w-8 h-8 mx-auto mb-3 ${isAutoMode ? 'text-white' : 'text-tesla-blue'}`} />
            <div className="font-medium">Auto</div>
            <div className="text-sm opacity-75">Automatic climate</div>
          </button>

          <button
            onClick={() => setIsAcOn(!isAcOn)}
            className={`card-tesla text-center p-6 transition-all ${
              isAcOn ? 'bg-blue-500 text-white' : 'hover:bg-gray-50'
            }`}
          >
            <AirConditioningIcon className={`w-8 h-8 mx-auto mb-3 ${isAcOn ? 'text-white' : 'text-blue-500'}`} />
            <div className="font-medium">A/C</div>
            <div className="text-sm opacity-75">Air conditioning</div>
          </button>

          <button
            onClick={() => setIsRecirculating(!isRecirculating)}
            className={`card-tesla text-center p-6 transition-all ${
              isRecirculating ? 'bg-green-500 text-white' : 'hover:bg-gray-50'
            }`}
          >
            <RecirculationIcon className={`w-8 h-8 mx-auto mb-3 ${isRecirculating ? 'text-white' : 'text-green-500'}`} />
            <div className="font-medium">Recirculate</div>
            <div className="text-sm opacity-75">Interior air</div>
          </button>

          <button className="card-tesla text-center p-6 hover:bg-gray-50 transition-colors">
            <DefrostIcon className="w-8 h-8 text-orange-500 mx-auto mb-3" />
            <div className="font-medium">Defrost</div>
            <div className="text-sm text-gray-600">Windshield</div>
          </button>
        </div>

        {/* Additional Controls */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="card-tesla">
            <h3 className="text-lg font-semibold text-tesla-darkGray mb-4">Climate Presets</h3>
            <div className="space-y-3">
              <button className="w-full p-3 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors text-left">
                <div className="flex items-center space-x-3">
                  <MaxAcIcon className="w-6 h-6 text-blue-500" />
                  <div>
                    <div className="font-medium">Max Cool</div>
                    <div className="text-sm text-gray-600">Quick cooling</div>
                  </div>
                </div>
              </button>
              <button className="w-full p-3 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors text-left">
                <div className="flex items-center space-x-3">
                  <HeaterIcon className="w-6 h-6 text-red-500" />
                  <div>
                    <div className="font-medium">Max Heat</div>
                    <div className="text-sm text-gray-600">Quick heating</div>
                  </div>
                </div>
              </button>
            </div>
          </div>

          <div className="card-tesla">
            <h3 className="text-lg font-semibold text-tesla-darkGray mb-4">Energy Efficiency</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Current draw:</span>
                <span className="font-medium">2.1 kW</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Range impact:</span>
                <span className="font-medium text-orange-600">-12 miles</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Eco mode:</span>
                <button className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm">
                  Enabled
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 