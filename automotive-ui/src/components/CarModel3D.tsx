import React, { useState, useEffect } from 'react';

interface CarControls {
  frontLeft: boolean;
  frontRight: boolean;
  rearLeft: boolean;
  rearRight: boolean;
  trunk: boolean;
  hood: boolean;
  headlights: boolean;
  hazards: boolean;
}

// Modern SVG Icons
const DoorIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-2-13h4v6h-4z"/>
  </svg>
);

const LockIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/>
  </svg>
);

const UnlockIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6h1.9c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z"/>
  </svg>
);

export const CarModel3D: React.FC = () => {
  const [controls, setControls] = useState<CarControls>({
    frontLeft: false,
    frontRight: false,
    rearLeft: false,
    rearRight: false,
    trunk: false,
    hood: false,
    headlights: true,
    hazards: false
  });

  const [isLocked, setIsLocked] = useState(true);
  const [animationPhase, setAnimationPhase] = useState(0);

  // Smooth animation cycle
  useEffect(() => {
    const interval = setInterval(() => {
      setAnimationPhase(prev => (prev + 1) % 360);
    }, 50);
    return () => clearInterval(interval);
  }, []);

  const toggleControl = (key: keyof CarControls) => {
    setControls(prev => ({ ...prev, [key]: !prev[key] }));
  };

  return (
    <div className="h-full flex flex-col items-center justify-center p-8 text-white relative">
      {/* 3D Car Model */}
      <div className="relative mb-8 transform-gpu perspective-1000" style={{ transform: `rotateY(${Math.sin(animationPhase * 0.01) * 5}deg)` }}>
        <svg 
          width="400" 
          height="240" 
          viewBox="0 0 400 240" 
          className="filter drop-shadow-2xl"
          style={{
            transform: `rotateX(${Math.sin(animationPhase * 0.005) * 2}deg)`,
          }}
        >
          {/* Car Shadow */}
          <ellipse 
            cx="200" 
            cy="220" 
            rx="160" 
            ry="20" 
            fill="rgba(0,0,0,0.2)" 
            className="animate-pulse"
          />
          
          {/* Main Car Body - 3D Effect */}
          <defs>
            <linearGradient id="carBodyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="rgba(255,255,255,0.3)" />
              <stop offset="50%" stopColor="rgba(255,255,255,0.15)" />
              <stop offset="100%" stopColor="rgba(255,255,255,0.05)" />
            </linearGradient>
            <linearGradient id="carBodyShadow" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="rgba(0,0,0,0.1)" />
              <stop offset="100%" stopColor="rgba(0,0,0,0.3)" />
            </linearGradient>
            <filter id="glow">
              <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
              <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>

          {/* Car Body Base */}
          <path
            d="M60 120 L80 80 L100 60 L300 60 L320 80 L340 120 L340 160 L320 170 L80 170 L60 160 Z"
            fill="url(#carBodyGradient)"
            stroke="rgba(255,255,255,0.4)"
            strokeWidth="2"
            className="transition-all duration-500"
          />
          
          {/* Car Body 3D Top */}
          <path
            d="M60 120 L80 80 L100 60 L300 60 L320 80 L340 120 L320 110 L100 110 L80 120 Z"
            fill="rgba(255,255,255,0.2)"
            stroke="rgba(255,255,255,0.3)"
            strokeWidth="1"
          />
          
          {/* Windshield & Windows */}
          <path
            d="M90 80 L110 65 L290 65 L310 80 L290 85 L110 85 Z"
            fill="rgba(100,200,255,0.3)"
            stroke="rgba(100,200,255,0.5)"
            strokeWidth="2"
            className="transition-all duration-300"
          />
          
          {/* Side Windows */}
          <polygon 
            points="110,85 140,85 140,110 110,110" 
            fill="rgba(100,200,255,0.2)" 
            stroke="rgba(100,200,255,0.4)"
          />
          <polygon 
            points="260,85 290,85 290,110 260,110" 
            fill="rgba(100,200,255,0.2)" 
            stroke="rgba(100,200,255,0.4)"
          />
          
          {/* Hood & Trunk Lines */}
          <line x1="120" y1="60" x2="120" y2="120" stroke="rgba(255,255,255,0.2)" strokeWidth="1"/>
          <line x1="280" y1="60" x2="280" y2="120" stroke="rgba(255,255,255,0.2)" strokeWidth="1"/>
          
          {/* Wheels - 3D Effect */}
          <circle cx="120" cy="160" r="25" fill="rgba(40,40,40,0.9)" stroke="rgba(200,200,200,0.8)" strokeWidth="3"/>
          <circle cx="120" cy="160" r="18" fill="rgba(100,100,100,0.8)" stroke="rgba(255,255,255,0.6)" strokeWidth="2"/>
          <circle cx="120" cy="160" r="8" fill="rgba(200,200,200,0.9)"/>
          
          <circle cx="280" cy="160" r="25" fill="rgba(40,40,40,0.9)" stroke="rgba(200,200,200,0.8)" strokeWidth="3"/>
          <circle cx="280" cy="160" r="18" fill="rgba(100,100,100,0.8)" stroke="rgba(255,255,255,0.6)" strokeWidth="2"/>
          <circle cx="280" cy="160" r="8" fill="rgba(200,200,200,0.9)"/>
          
          {/* Headlights - Enhanced */}
          <ellipse 
            cx="350" 
            cy="100" 
            rx="12" 
            ry="8" 
            fill={controls.headlights ? "rgba(255,255,150,0.95)" : "rgba(255,255,255,0.3)"}
            filter={controls.headlights ? "url(#glow)" : ""}
            className="transition-all duration-300"
          />
          <ellipse 
            cx="350" 
            cy="120" 
            rx="12" 
            ry="8" 
            fill={controls.headlights ? "rgba(255,255,150,0.95)" : "rgba(255,255,255,0.3)"}
            filter={controls.headlights ? "url(#glow)" : ""}
            className="transition-all duration-300"
          />
          
          {/* Taillights */}
          <ellipse cx="50" cy="110" rx="8" ry="6" fill="rgba(255,100,100,0.7)"/>
          <ellipse cx="50" cy="125" rx="8" ry="6" fill="rgba(255,100,100,0.7)"/>
          
          {/* Door Handles */}
          <rect x="140" y="105" width="8" height="4" rx="2" fill="rgba(200,200,200,0.8)"/>
          <rect x="250" y="105" width="8" height="4" rx="2" fill="rgba(200,200,200,0.8)"/>
          
          {/* Door Indicators */}
          {controls.frontLeft && (
            <path d="M110 80 L130 70" stroke="rgba(0,255,100,0.9)" strokeWidth="4" className="animate-pulse"/>
          )}
          {controls.frontRight && (
            <path d="M270 70 L290 80" stroke="rgba(0,255,100,0.9)" strokeWidth="4" className="animate-pulse"/>
          )}
          
          {/* Hazard Lights */}
          {controls.hazards && (
            <>
              <circle cx="70" cy="110" r="6" fill="rgba(255,165,0,0.95)" className="animate-pulse"/>
              <circle cx="330" cy="110" r="6" fill="rgba(255,165,0,0.95)" className="animate-pulse"/>
            </>
          )}
          
          {/* Grille */}
          <rect x="340" y="95" width="15" height="30" rx="2" fill="rgba(100,100,100,0.8)" stroke="rgba(200,200,200,0.6)"/>
          <line x1="342" y1="100" x2="352" y2="100" stroke="rgba(255,255,255,0.4)"/>
          <line x1="342" y1="110" x2="352" y2="110" stroke="rgba(255,255,255,0.4)"/>
          <line x1="342" y1="120" x2="352" y2="120" stroke="rgba(255,255,255,0.4)"/>
        </svg>

        {/* Lock Status Indicator */}
        <div className="absolute -top-12 left-1/2 transform -translate-x-1/2">
          <div className={`flex items-center space-x-3 px-4 py-2 rounded-2xl text-sm backdrop-blur-sm ${
            isLocked 
              ? 'bg-red-500/20 text-red-300 border border-red-500/30' 
              : 'bg-green-500/20 text-green-300 border border-green-500/30'
          }`}>
            {isLocked ? <LockIcon className="w-5 h-5" /> : <UnlockIcon className="w-5 h-5" />}
            <span className="font-medium">{isLocked ? 'Locked' : 'Unlocked'}</span>
          </div>
        </div>
      </div>

      {/* Control Panel */}
      <div className="grid grid-cols-2 gap-6 w-full max-w-lg">
        {/* Lock/Unlock */}
        <button
          onClick={() => setIsLocked(!isLocked)}
          className={`flex items-center justify-center space-x-3 p-6 rounded-3xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 active:scale-95 ${
            isLocked 
              ? 'bg-red-500/20 text-red-300 border-2 border-red-500/30 hover:bg-red-500/30' 
              : 'bg-green-500/20 text-green-300 border-2 border-green-500/30 hover:bg-green-500/30'
          }`}
        >
          {isLocked ? <LockIcon className="w-6 h-6" /> : <UnlockIcon className="w-6 h-6" />}
          <span>{isLocked ? 'Unlock' : 'Lock'}</span>
        </button>

        {/* Lights */}
        <button
          onClick={() => toggleControl('headlights')}
          className={`flex items-center justify-center space-x-3 p-6 rounded-3xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 active:scale-95 ${
            controls.headlights 
              ? 'bg-yellow-500/20 text-yellow-300 border-2 border-yellow-500/30 hover:bg-yellow-500/30' 
              : 'bg-white/5 text-white/60 border-2 border-white/10 hover:bg-white/10'
          }`}
        >
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1zm3-19C8.14 2 5 5.14 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.14-7-7-7z"/>
          </svg>
          <span>Lights</span>
        </button>

        {/* Trunk */}
        <button
          onClick={() => toggleControl('trunk')}
          className={`flex items-center justify-center space-x-3 p-6 rounded-3xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 active:scale-95 ${
            controls.trunk 
              ? 'bg-blue-500/20 text-blue-300 border-2 border-blue-500/30 hover:bg-blue-500/30' 
              : 'bg-white/5 text-white/60 border-2 border-white/10 hover:bg-white/10'
          }`}
        >
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99z"/>
          </svg>
          <span>Trunk</span>
        </button>

        {/* Hazards */}
        <button
          onClick={() => toggleControl('hazards')}
          className={`flex items-center justify-center space-x-3 p-6 rounded-3xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 active:scale-95 ${
            controls.hazards 
              ? 'bg-orange-500/20 text-orange-300 border-2 border-orange-500/30 hover:bg-orange-500/30' 
              : 'bg-white/5 text-white/60 border-2 border-white/10 hover:bg-white/10'
          }`}
        >
          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M1 21h4L5 7l4-4L6 2 2 6 1 21zm5-18L12 9V7l6-6-1-1-6 2-3 1zm6 11l6 6V16l-4-4-2 2z"/>
          </svg>
          <span>Hazards</span>
        </button>
      </div>
    </div>
  );
}; 