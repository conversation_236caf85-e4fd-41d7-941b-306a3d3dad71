import React, { useState, useEffect } from 'react';

interface VehicleStatus {
  starterBattery: number; // Voltage
  auxBattery: number; // Voltage
  speed: number;
  fuelLevel: number; // Percentage
  range: number;
  engineTemp: number;
  isRunning: boolean;
}

// Custom SVG Icons
const BatteryVoltageIcon: React.FC<{ voltage: number; type: 'starter' | 'aux' }> = ({ voltage, type }) => {
  const getColor = () => {
    if (type === 'starter') {
      if (voltage >= 12.4) return '#10B981'; // green-500
      if (voltage >= 11.8) return '#F59E0B'; // yellow-500
      return '#EF4444'; // red-500
    } else {
      if (voltage >= 12.0) return '#10B981';
      if (voltage >= 11.5) return '#F59E0B';
      return '#EF4444';
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <svg className="w-6 h-3" viewBox="0 0 24 12" fill="none">
        <rect x="1" y="2" width="20" height="8" rx="1" stroke="currentColor" strokeWidth="1" fill="none"/>
        <rect x="22" y="4" width="1" height="4" rx="0.5" fill="currentColor"/>
        <rect 
          x="2" 
          y="3" 
          width={`${Math.min(18, (voltage / 14) * 18)}`} 
          height="6" 
          rx="0.5" 
          fill={getColor()}
        />
      </svg>
      <span className="text-xs font-medium" style={{ color: getColor() }}>
        {voltage.toFixed(1)}V
      </span>
    </div>
  );
};

const FuelIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M19.77 7.23l.01-.01-3.72-3.72L15 4.56l2.11 2.11c-.94.36-1.61 1.26-1.61 2.33 0 1.38 1.12 2.5 2.5 2.5.36 0 .69-.08 1-.21v7.21c0 .55-.45 1-1 1s-1-.45-1-1V14c0-1.1-.9-2-2-2h-1V5c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h6c1.1 0 2-.9 2-2v-3h1v5.5c0 1.38 1.12 2.5 2.5 2.5s2.5-1.12 2.5-2.5V9c0-.69-.28-1.32-.73-1.77zM12 13.5V19H6v-7h6v1.5z"/>
  </svg>
);

const EngineIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
  </svg>
);

const SpeedIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12,1A11,11 0 0,0 1,12A11,11 0 0,0 12,23A11,11 0 0,0 23,12A11,11 0 0,0 12,1M12,3A9,9 0 0,1 21,12A9,9 0 0,1 12,21A9,9 0 0,1 3,12A9,9 0 0,1 12,3M13,7V13H7V11H11V7H13Z"/>
  </svg>
);

const TurboLogo = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 32 32" fill="currentColor">
    <circle cx="16" cy="16" r="14" fill="none" stroke="currentColor" strokeWidth="2"/>
    <path d="M8 16h6l4-6v4h6l-6 10h-4v-4l-6-4z"/>
  </svg>
);

export const VehicleStatusBar: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [vehicleStatus, setVehicleStatus] = useState<VehicleStatus>({
    starterBattery: 12.6,
    auxBattery: 12.2,
    speed: 0,
    fuelLevel: 68,
    range: 287,
    engineTemp: 185,
    isRunning: true
  });

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Simulate vehicle data updates
  useEffect(() => {
    const dataTimer = setInterval(() => {
      setVehicleStatus(prev => ({
        ...prev,
        speed: Math.max(0, prev.speed + (Math.random() - 0.5) * 5),
        starterBattery: Math.max(11.0, Math.min(14.4, prev.starterBattery + (Math.random() - 0.5) * 0.02)),
        auxBattery: Math.max(10.5, Math.min(13.8, prev.auxBattery + (Math.random() - 0.5) * 0.02)),
        fuelLevel: Math.max(0, Math.min(100, prev.fuelLevel + (Math.random() - 0.5) * 0.1)),
        range: Math.max(0, prev.range + (Math.random() - 0.5) * 2),
        engineTemp: Math.max(160, Math.min(220, prev.engineTemp + (Math.random() - 0.5) * 1))
      }));
    }, 3000);
    return () => clearInterval(dataTimer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-gray-950 text-white px-8 py-4 flex items-center justify-between backdrop-blur-xl border-b border-gray-800/50">
      {/* Left side - Vehicle stats */}
      <div className="flex items-center space-x-8">
        {/* Starter Battery */}
        <div className="flex items-center space-x-3">
          <div className="text-xs text-gray-400 uppercase tracking-wide">Starter</div>
          <BatteryVoltageIcon voltage={vehicleStatus.starterBattery} type="starter" />
        </div>

        {/* Auxiliary Battery */}
        <div className="flex items-center space-x-3">
          <div className="text-xs text-gray-400 uppercase tracking-wide">Aux</div>
          <BatteryVoltageIcon voltage={vehicleStatus.auxBattery} type="aux" />
        </div>

        {/* Speed */}
        <div className="flex items-center space-x-3">
          <SpeedIcon className="w-5 h-5 text-blue-400" />
          <div className="flex items-baseline space-x-1">
            <span className="text-2xl font-light">{Math.round(vehicleStatus.speed)}</span>
            <span className="text-sm text-gray-400">mph</span>
          </div>
        </div>

        {/* Fuel */}
        <div className="flex items-center space-x-3">
          <FuelIcon className="w-5 h-5 text-orange-400" />
          <div className="flex items-center space-x-1">
            <span className="text-sm font-medium">{Math.round(vehicleStatus.fuelLevel)}%</span>
            <span className="text-sm text-gray-400">• {Math.round(vehicleStatus.range)} mi</span>
          </div>
        </div>

        {/* Engine Temp */}
        <div className="flex items-center space-x-3">
          <EngineIcon className="w-5 h-5 text-green-400" />
          <span className="text-sm font-medium">{Math.round(vehicleStatus.engineTemp)}°F</span>
        </div>
      </div>

      {/* Center - Turbo logo */}
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
          <TurboLogo className="w-6 h-6 text-gray-950" />
        </div>
        <div className="text-lg font-semibold tracking-wide">TURBO</div>
      </div>

      {/* Right side - Time and date */}
      <div className="text-right">
        <div className="text-xl font-light">{formatTime(currentTime)}</div>
        <div className="text-sm text-gray-400">{formatDate(currentTime)}</div>
      </div>
    </div>
  );
}; 