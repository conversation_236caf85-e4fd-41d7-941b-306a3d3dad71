import React, { useState, useEffect } from 'react';
import {
  Battery,
  Gauge,
  Fuel,
  Thermometer,
  Zap,
  Clock
} from 'lucide-react';

interface VehicleStatus {
  starterBattery: number; // Voltage
  auxBattery: number; // Voltage
  speed: number;
  fuelLevel: number; // Percentage
  range: number;
  engineTemp: number;
  isRunning: boolean;
}

// Enhanced Battery Component with better visual feedback
const BatteryVoltageIcon: React.FC<{ voltage: number; type: 'starter' | 'aux' }> = ({ voltage, type }) => {
  const getStatus = () => {
    if (type === 'starter') {
      if (voltage >= 12.4) return { color: 'text-success', bgColor: 'bg-success', level: 'good' };
      if (voltage >= 11.8) return { color: 'text-warning', bgColor: 'bg-warning', level: 'warning' };
      return { color: 'text-error', bgColor: 'bg-error', level: 'critical' };
    } else {
      if (voltage >= 12.0) return { color: 'text-success', bgColor: 'bg-success', level: 'good' };
      if (voltage >= 11.5) return { color: 'text-warning', bgColor: 'bg-warning', level: 'warning' };
      return { color: 'text-error', bgColor: 'bg-error', level: 'critical' };
    }
  };

  const status = getStatus();
  const batteryLevel = Math.min(100, Math.max(0, ((voltage - 10) / 4) * 100));

  return (
    <div className="flex items-center space-x-3">
      <div className="relative">
        <Battery className={`w-6 h-6 ${status.color} transition-colors duration-300`} />
        <div
          className={`absolute inset-0 ${status.bgColor} opacity-20 rounded transition-opacity duration-300`}
          style={{
            clipPath: `inset(${100 - batteryLevel}% 0 0 0)`
          }}
        />
      </div>
      <div className="flex flex-col">
        <span className={`text-sm font-semibold ${status.color} transition-colors duration-300`}>
          {voltage.toFixed(1)}V
        </span>
        <span className="text-xs text-neutral-400 uppercase tracking-wide">
          {status.level}
        </span>
      </div>
    </div>
  );
};

// Modern Turbo Logo with enhanced styling
const TurboLogo = ({ className }: { className?: string }) => (
  <div className="relative">
    <Zap className={`${className} transition-all duration-300`} />
    <div className="absolute inset-0 bg-gradient-to-r from-primary to-primary-light opacity-20 rounded-full blur-sm" />
  </div>
);

export const VehicleStatusBar: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [vehicleStatus, setVehicleStatus] = useState<VehicleStatus>({
    starterBattery: 12.6,
    auxBattery: 12.2,
    speed: 0,
    fuelLevel: 68,
    range: 287,
    engineTemp: 185,
    isRunning: true
  });

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Simulate vehicle data updates
  useEffect(() => {
    const dataTimer = setInterval(() => {
      setVehicleStatus(prev => ({
        ...prev,
        speed: Math.max(0, prev.speed + (Math.random() - 0.5) * 5),
        starterBattery: Math.max(11.0, Math.min(14.4, prev.starterBattery + (Math.random() - 0.5) * 0.02)),
        auxBattery: Math.max(10.5, Math.min(13.8, prev.auxBattery + (Math.random() - 0.5) * 0.02)),
        fuelLevel: Math.max(0, Math.min(100, prev.fuelLevel + (Math.random() - 0.5) * 0.1)),
        range: Math.max(0, prev.range + (Math.random() - 0.5) * 2),
        engineTemp: Math.max(160, Math.min(220, prev.engineTemp + (Math.random() - 0.5) * 1))
      }));
    }, 3000);
    return () => clearInterval(dataTimer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-neutral-950 text-white px-8 py-5 flex items-center justify-between backdrop-blur-xl border-b border-neutral-800/50 animate-fade-in">
      {/* Left side - Vehicle stats */}
      <div className="flex items-center space-x-10">
        {/* Starter Battery */}
        <div className="flex flex-col space-y-1">
          <div className="text-xs text-neutral-400 uppercase tracking-wider font-medium">Starter</div>
          <BatteryVoltageIcon voltage={vehicleStatus.starterBattery} type="starter" />
        </div>

        {/* Auxiliary Battery */}
        <div className="flex flex-col space-y-1">
          <div className="text-xs text-neutral-400 uppercase tracking-wider font-medium">Auxiliary</div>
          <BatteryVoltageIcon voltage={vehicleStatus.auxBattery} type="aux" />
        </div>

        {/* Speed */}
        <div className="flex items-center space-x-4">
          <div className="p-2 bg-primary/10 rounded-xl">
            <Gauge className="w-6 h-6 text-primary transition-colors duration-300" />
          </div>
          <div className="flex flex-col">
            <div className="flex items-baseline space-x-1">
              <span className="text-3xl font-light tracking-tight">{Math.round(vehicleStatus.speed)}</span>
              <span className="text-sm text-neutral-400 font-medium">mph</span>
            </div>
            <span className="text-xs text-neutral-500 uppercase tracking-wide">Speed</span>
          </div>
        </div>

        {/* Fuel */}
        <div className="flex items-center space-x-4">
          <div className="p-2 bg-warning/10 rounded-xl">
            <Fuel className="w-6 h-6 text-warning transition-colors duration-300" />
          </div>
          <div className="flex flex-col">
            <div className="flex items-center space-x-2">
              <span className="text-lg font-semibold">{Math.round(vehicleStatus.fuelLevel)}%</span>
              <span className="text-sm text-neutral-400">• {Math.round(vehicleStatus.range)} mi</span>
            </div>
            <span className="text-xs text-neutral-500 uppercase tracking-wide">Fuel & Range</span>
          </div>
        </div>

        {/* Engine Temp */}
        <div className="flex items-center space-x-4">
          <div className="p-2 bg-success/10 rounded-xl">
            <Thermometer className="w-6 h-6 text-success transition-colors duration-300" />
          </div>
          <div className="flex flex-col">
            <span className="text-lg font-semibold">{Math.round(vehicleStatus.engineTemp)}°F</span>
            <span className="text-xs text-neutral-500 uppercase tracking-wide">Engine Temp</span>
          </div>
        </div>
      </div>

      {/* Center - Turbo logo */}
      <div className="flex items-center space-x-4 px-6 py-3 bg-white/5 rounded-2xl backdrop-blur-sm border border-white/10 hover-glow">
        <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary-light rounded-xl flex items-center justify-center shadow-lg">
          <TurboLogo className="w-7 h-7 text-white" />
        </div>
        <div className="text-xl font-bold tracking-wider bg-gradient-to-r from-white to-neutral-300 bg-clip-text text-transparent">
          TURBO
        </div>
      </div>

      {/* Right side - Time and date */}
      <div className="text-right space-y-1">
        <div className="flex items-center space-x-2 justify-end">
          <Clock className="w-5 h-5 text-neutral-400" />
          <div className="text-2xl font-light tracking-tight">{formatTime(currentTime)}</div>
        </div>
        <div className="text-sm text-neutral-400 font-medium">{formatDate(currentTime)}</div>
      </div>
    </div>
  );
}; 