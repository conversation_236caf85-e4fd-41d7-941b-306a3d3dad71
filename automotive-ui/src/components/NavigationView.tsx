import React, { useState } from 'react';
import {
  Search,
  MapPin,
  Navigation as NavigationIcon,
  Map,
  Compass,
  Clock,
  Fuel,
  DollarSign
} from 'lucide-react';



export const NavigationView: React.FC = () => {
  const [destination, setDestination] = useState('');
  const [currentLocation] = useState('1234 Main Street, City, State');

  return (
    <div className="h-full flex flex-col bg-neutral-50 animate-fade-in">
      {/* Header Section */}
      <div className="p-8 space-y-8 flex-shrink-0">
        {/* Search input */}
        <div className="space-y-6 animate-slide-up">
          <h2 className="text-4xl font-bold text-neutral-900 tracking-tight">Where to?</h2>
          <div className="relative">
            <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 w-7 h-7 text-neutral-400" strokeWidth={2} />
            <input
              type="text"
              value={destination}
              onChange={(e) => setDestination(e.target.value)}
              placeholder="Search destination or address"
              className="input-large w-full pl-20 pr-6 hover-lift focus:scale-[1.02] transition-all duration-300"
            />
          </div>
        </div>

        {/* Current location */}
        <div className="card-glass p-8 animate-slide-up" style={{ animationDelay: '100ms' }}>
          <div className="flex items-start space-x-6">
            <div className="w-16 h-16 bg-primary/10 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-sm hover-scale transition-all duration-300">
              <MapPin className="w-8 h-8 text-primary" strokeWidth={2.5} />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-2xl font-bold text-neutral-900 mb-3">Current Location</h3>
              <p className="text-lg text-neutral-700 break-words leading-relaxed mb-4">{currentLocation}</p>
              <div className="flex items-center text-success bg-success/10 px-4 py-2 rounded-xl w-fit">
                <Compass className="w-5 h-5 mr-3 animate-pulse-slow" strokeWidth={2.5} />
                <span className="font-semibold">GPS Connected • Signal Strong</span>
              </div>
            </div>
          </div>
        </div>

        {/* Trip Summary */}
        <div className="card p-8 hover-lift animate-slide-up" style={{ animationDelay: '200ms' }}>
          <div className="flex items-center justify-between mb-8">
            <h3 className="text-2xl font-bold text-neutral-900">Trip Summary</h3>
            <div className="w-14 h-14 bg-success/10 rounded-2xl flex items-center justify-center hover-scale transition-all duration-300">
              <NavigationIcon className="w-7 h-7 text-success" strokeWidth={2.5} />
            </div>
          </div>

          <div className="grid grid-cols-3 gap-6">
            <div className="text-center p-6 bg-neutral-50 rounded-2xl hover-lift transition-all duration-300">
              <div className="text-3xl font-bold text-neutral-900 mb-2">12.4</div>
              <div className="text-neutral-600 font-semibold uppercase tracking-wide text-sm">Miles</div>
            </div>
            <div className="text-center p-6 bg-neutral-50 rounded-2xl hover-lift transition-all duration-300">
              <div className="flex items-center justify-center mb-2">
                <Clock className="w-6 h-6 text-primary mr-2" strokeWidth={2.5} />
                <span className="text-3xl font-bold text-neutral-900">18</span>
              </div>
              <div className="text-neutral-600 font-semibold uppercase tracking-wide text-sm">Minutes</div>
            </div>
            <div className="text-center p-6 bg-success/5 rounded-2xl hover-lift transition-all duration-300 border border-success/20">
              <div className="flex items-center justify-center mb-2">
                <DollarSign className="w-6 h-6 text-success mr-1" strokeWidth={2.5} />
                <span className="text-3xl font-bold text-success">3.20</span>
              </div>
              <div className="text-neutral-600 font-semibold uppercase tracking-wide text-sm">Fuel Cost</div>
            </div>
          </div>
        </div>
      </div>

      {/* Map Area */}
      <div className="flex-1 relative bg-gradient-to-br from-primary/5 via-white to-neutral-50 m-8 mt-0 rounded-3xl overflow-hidden border-2 border-neutral-200 shadow-xl animate-slide-up" style={{ animationDelay: '300ms' }}>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center max-w-lg px-8">
            <div className="w-32 h-32 bg-gradient-to-br from-primary/10 to-primary/20 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-xl hover-scale transition-all duration-500">
              <Map className="w-16 h-16 text-primary" strokeWidth={2} />
            </div>
            <h3 className="text-3xl font-bold text-neutral-900 mb-6 tracking-tight">
              Navigation Ready
            </h3>
            <p className="text-xl text-neutral-600 mb-10 leading-relaxed">
              High-precision GPS navigation with real-time traffic updates and intelligent route optimization.
            </p>
            <div className="flex items-center justify-center space-x-4 text-success bg-success/10 px-8 py-4 rounded-2xl border border-success/20 hover-glow transition-all duration-300">
              <div className="w-4 h-4 bg-success rounded-full animate-pulse-slow"></div>
              <span className="font-bold text-lg">GPS Signal Strong</span>
            </div>
          </div>
        </div>

        {/* Floating Action Button */}
        <div className="absolute bottom-8 right-8">
          <button className="btn-primary w-18 h-18 rounded-2xl shadow-2xl hover:shadow-primary/25 transition-all duration-300 transform hover:scale-110 active:scale-95 flex items-center justify-center group">
            <NavigationIcon className="w-9 h-9 group-hover:scale-110 transition-transform duration-300" strokeWidth={2.5} />
          </button>
        </div>

        {/* Status Pills */}
        <div className="absolute top-6 left-6 flex space-x-4">
          <div className="glass px-6 py-3 rounded-2xl shadow-lg hover-lift transition-all duration-300">
            <div className="flex items-center space-x-3 text-sm font-semibold text-neutral-700">
              <div className="w-3 h-3 bg-primary rounded-full animate-pulse"></div>
              <span>Live Traffic</span>
            </div>
          </div>
          <div className="glass px-6 py-3 rounded-2xl shadow-lg hover-lift transition-all duration-300">
            <div className="flex items-center space-x-3 text-sm font-semibold text-neutral-700">
              <div className="w-3 h-3 bg-success rounded-full animate-pulse"></div>
              <span>Optimized Route</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 