import React, { useState } from 'react';

// Modern Custom SVG Icons
const HomeIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
    <path d="M3 9L12 2L21 9V20A2 2 0 0119 22H5A2 2 0 013 20V9Z"/>
    <polyline points="9,22 9,12 15,12 15,22"/>
  </svg>
);

const WorkIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
    <line x1="8" y1="21" x2="16" y2="21"/>
    <line x1="12" y1="17" x2="12" y2="21"/>
  </svg>
);

const FuelIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
    <path d="M3 7V19A2 2 0 005 21H14A2 2 0 0016 19V7A2 2 0 0014 5H5A2 2 0 003 7Z"/>
    <polyline points="16,11 20,7 21,8 17,12"/>
    <line x1="7" y1="10" x2="13" y2="10"/>
    <line x1="7" y1="14" x2="13" y2="14"/>
  </svg>
);

const ShoppingIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
    <circle cx="9" cy="21" r="1"/>
    <circle cx="20" cy="21" r="1"/>
    <path d="M1 1H5L7.68 14.39A2 2 0 009.65 16H19.4A2 2 0 0021.38 14.39L23 6H6"/>
  </svg>
);

const SearchIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
    <circle cx="11" cy="11" r="8"/>
    <path d="M21 21L16.65 16.65"/>
  </svg>
);

const LocationIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
    <path d="M21 10C21 17L12 23L3 10C3 5.58 7.03 2 12 2S21 5.58 21 10Z"/>
    <circle cx="12" cy="10" r="3"/>
  </svg>
);

const MapIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
    <polygon points="1,6 1,22 8,18 16,22 23,18 23,2 16,6 8,2 1,6"/>
    <line x1="8" y1="2" x2="8" y2="18"/>
    <line x1="16" y1="6" x2="16" y2="22"/>
  </svg>
);

const GpsIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
    <circle cx="12" cy="12" r="10"/>
    <polygon points="16.24,7.76 14.12,14.12 7.76,16.24 9.88,9.88 16.24,7.76"/>
  </svg>
);

const NavigationArrowIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
    <polygon points="3,11 22,2 13,21 11,13 3,11"/>
  </svg>
);

export const NavigationView: React.FC = () => {
  const [destination, setDestination] = useState('');
  const [currentLocation] = useState('1234 Main Street, City, State');

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header Section */}
      <div className="p-8 space-y-8 flex-shrink-0">
        {/* Search input */}
        <div className="space-y-6">
          <h2 className="text-3xl font-semibold text-gray-900">Where to?</h2>
          <div className="relative">
            <SearchIcon className="absolute left-6 top-1/2 transform -translate-y-1/2 w-6 h-6 text-gray-400" />
            <input
              type="text"
              value={destination}
              onChange={(e) => setDestination(e.target.value)}
              placeholder="Search destination or address"
              className="w-full pl-16 pr-6 py-6 text-xl bg-white border-2 border-gray-200 rounded-3xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all shadow-sm hover:shadow-md"
            />
          </div>
        </div>

        {/* Current location */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-3xl p-8 border-2 border-blue-100 shadow-sm">
          <div className="flex items-start space-x-6">
            <div className="w-14 h-14 bg-blue-100 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-sm">
              <LocationIcon className="w-7 h-7 text-blue-600" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Current Location</h3>
              <p className="text-lg text-gray-700 break-words leading-relaxed">{currentLocation}</p>
              <div className="mt-4 flex items-center text-green-600">
                <GpsIcon className="w-5 h-5 mr-3" />
                <span className="font-medium">GPS Connected • Signal Strong</span>
              </div>
            </div>
          </div>
        </div>

        {/* Trip Summary */}
        <div className="bg-white rounded-3xl p-8 border-2 border-gray-100 shadow-sm">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-gray-900">Trip Summary</h3>
            <div className="w-12 h-12 bg-green-50 rounded-2xl flex items-center justify-center">
              <NavigationArrowIcon className="w-6 h-6 text-green-600" />
            </div>
          </div>
          
          <div className="grid grid-cols-3 gap-6">
            <div className="text-center p-4 bg-gray-50 rounded-2xl">
              <div className="text-2xl font-bold text-gray-900 mb-1">12.4</div>
              <div className="text-gray-600 font-medium">Miles</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-2xl">
              <div className="text-2xl font-bold text-gray-900 mb-1">18</div>
              <div className="text-gray-600 font-medium">Minutes</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-2xl">
              <div className="text-2xl font-bold text-green-600 mb-1">$3.20</div>
              <div className="text-gray-600 font-medium">Fuel Cost</div>
            </div>
          </div>
        </div>
      </div>

      {/* Map Area */}
      <div className="flex-1 relative bg-gradient-to-br from-blue-50 via-white to-slate-50 m-8 mt-0 rounded-3xl overflow-hidden border-2 border-gray-100 shadow-lg">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center max-w-md px-8">
            <div className="w-28 h-28 bg-gradient-to-br from-blue-100 to-blue-200 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg">
              <MapIcon className="w-14 h-14 text-blue-600" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Navigation Ready
            </h3>
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              High-precision GPS navigation with real-time traffic updates and intelligent route optimization.
            </p>
            <div className="flex items-center justify-center space-x-3 text-green-600 bg-green-50 px-6 py-3 rounded-2xl border border-green-200">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="font-semibold">GPS Signal Strong</span>
            </div>
          </div>
        </div>

        {/* Floating Action Button */}
        <div className="absolute bottom-8 right-8">
          <button className="w-16 h-16 bg-blue-600 hover:bg-blue-700 text-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 active:scale-95 flex items-center justify-center">
            <NavigationArrowIcon className="w-8 h-8" />
          </button>
        </div>

        {/* Status Pills */}
        <div className="absolute top-6 left-6 flex space-x-3">
          <div className="bg-white/90 backdrop-blur-sm px-4 py-2 rounded-2xl border border-white/20 shadow-lg">
            <div className="flex items-center space-x-2 text-sm font-medium text-gray-700">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>Live Traffic</span>
            </div>
          </div>
          <div className="bg-white/90 backdrop-blur-sm px-4 py-2 rounded-2xl border border-white/20 shadow-lg">
            <div className="flex items-center space-x-2 text-sm font-medium text-gray-700">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Optimized Route</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 