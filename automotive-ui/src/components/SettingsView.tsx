import React, { useState } from 'react';

// Custom SVG Icons
const AutopilotIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
  </svg>
);

const ChargingIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2l3.09 8.26L22 12l-6.91 1.74L12 22l-3.09-8.26L2 12l6.91-1.74L12 2z"/>
  </svg>
);

const SecurityIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2L4 5v6.09c0 5.05 3.41 9.76 8 10.91 4.59-1.15 8-5.86 8-10.91V5l-8-3zm0 18c-4.07-.82-7-4.37-7-8.09V6.41l7-2.8 7 2.8v5.5c0 3.72-2.93 7.27-7 8.09z"/>
  </svg>
);

const DisplayIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M21 3H3c-1.1 0-2 .9-2 2v11c0 1.1.9 2 2 2h6l-2 3v1h8v-1l-2-3h6c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 13H3V5h18v11z"/>
  </svg>
);

const WifiIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.08 2.93 1 9zm8 8l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z"/>
  </svg>
);

const BluetoothIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M17.71 7.71L12 2h-1v7.59L6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 11 14.41V22h1l5.71-5.71-4.3-4.29 4.3-4.29zM13 5.83l1.88 1.88L13 9.59V5.83zm1.88 10.46L13 18.17v-3.76l1.88 1.88z"/>
  </svg>
);

const SoundIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
  </svg>
);

const DoorsIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-2-13h4v6h-4z"/>
  </svg>
);

const TrunkIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/>
  </svg>
);

const LightsIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c.55 0 1 .45 1 1v6c0 .55-.45 1-1 1s-1-.45-1-1V6c0-.55.45-1 1-1zm0 14c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/>
  </svg>
);

const DrivingIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z"/>
  </svg>
);

const UpdateIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
  </svg>
);

const InfoIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
  </svg>
);

interface SettingItem {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  value?: boolean | number | string;
  type: 'toggle' | 'slider' | 'select' | 'button' | 'info';
  options?: string[];
  min?: number;
  max?: number;
}

export const SettingsView: React.FC = () => {
  const [settings, setSettings] = useState<Record<string, any>>({
    autopilot: true,
    autoSteer: false,
    summon: true,
    chargingLimit: 80,
    scheduledCharging: false,
    sentryMode: true,
    walkAwayLock: true,
    brightness: 75,
    soundVolume: 60,
    wifiEnabled: true,
    bluetoothEnabled: true,
    autoHeadlights: true,
    drivingMode: 'comfort'
  });

  const updateSetting = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const settingSections = [
    {
      title: 'Autopilot',
      icon: AutopilotIcon,
      items: [
        {
          id: 'autopilot',
          name: 'Full Self-Driving',
          description: 'Enable advanced autopilot features',
          icon: AutopilotIcon,
          type: 'toggle' as const
        },
        {
          id: 'autoSteer',
          name: 'Auto Steer',
          description: 'Automatic steering assistance',
          icon: DrivingIcon,
          type: 'toggle' as const
        },
        {
          id: 'summon',
          name: 'Smart Summon',
          description: 'Vehicle can navigate to you',
          icon: AutopilotIcon,
          type: 'toggle' as const
        }
      ]
    },
    {
      title: 'Charging',
      icon: ChargingIcon,
      items: [
        {
          id: 'chargingLimit',
          name: 'Charging Limit',
          description: 'Maximum charge level',
          icon: ChargingIcon,
          type: 'slider' as const,
          min: 50,
          max: 100
        },
        {
          id: 'scheduledCharging',
          name: 'Scheduled Charging',
          description: 'Charge during off-peak hours',
          icon: ChargingIcon,
          type: 'toggle' as const
        }
      ]
    },
    {
      title: 'Security',
      icon: SecurityIcon,
      items: [
        {
          id: 'sentryMode',
          name: 'Sentry Mode',
          description: 'Monitor surroundings when parked',
          icon: SecurityIcon,
          type: 'toggle' as const
        },
        {
          id: 'walkAwayLock',
          name: 'Walk-Away Door Lock',
          description: 'Auto lock when walking away',
          icon: DoorsIcon,
          type: 'toggle' as const
        }
      ]
    },
    {
      title: 'Display & Sound',
      icon: DisplayIcon,
      items: [
        {
          id: 'brightness',
          name: 'Screen Brightness',
          description: 'Adjust display brightness',
          icon: DisplayIcon,
          type: 'slider' as const,
          min: 10,
          max: 100
        },
        {
          id: 'soundVolume',
          name: 'System Volume',
          description: 'Control system sounds',
          icon: SoundIcon,
          type: 'slider' as const,
          min: 0,
          max: 100
        }
      ]
    },
    {
      title: 'Connectivity',
      icon: WifiIcon,
      items: [
        {
          id: 'wifiEnabled',
          name: 'Wi-Fi',
          description: 'Connect to wireless networks',
          icon: WifiIcon,
          type: 'toggle' as const
        },
        {
          id: 'bluetoothEnabled',
          name: 'Bluetooth',
          description: 'Connect devices via Bluetooth',
          icon: BluetoothIcon,
          type: 'toggle' as const
        }
      ]
    },
    {
      title: 'Vehicle Controls',
      icon: DrivingIcon,
      items: [
        {
          id: 'autoHeadlights',
          name: 'Auto High Beams',
          description: 'Automatically control headlights',
          icon: LightsIcon,
          type: 'toggle' as const
        },
        {
          id: 'drivingMode',
          name: 'Driving Mode',
          description: 'Select driving characteristics',
          icon: DrivingIcon,
          type: 'select' as const,
          options: ['comfort', 'sport', 'chill']
        }
      ]
    }
  ];

  const systemInfo = [
    { label: 'Software Version', value: '2024.2.7' },
    { label: 'Last Update', value: 'January 15, 2024' },
    { label: 'Vehicle ID', value: 'TESLA2024DEMO' },
    { label: 'Connectivity', value: 'Premium' }
  ];

  const renderSettingControl = (item: SettingItem) => {
    const value = settings[item.id];

    switch (item.type) {
      case 'toggle':
        return (
          <button
            onClick={() => updateSetting(item.id, !value)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              value ? 'bg-tesla-blue' : 'bg-gray-300'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                value ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        );

      case 'slider':
        return (
          <div className="flex items-center space-x-3 min-w-[200px]">
            <input
              type="range"
              min={item.min}
              max={item.max}
              value={value}
              onChange={(e) => updateSetting(item.id, parseInt(e.target.value))}
              className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <span className="text-sm font-medium text-gray-600 min-w-[40px]">
              {value}{item.id.includes('Limit') ? '%' : ''}
            </span>
          </div>
        );

      case 'select':
        return (
          <select
            value={value}
            onChange={(e) => updateSetting(item.id, e.target.value)}
            className="px-3 py-1 border border-gray-300 rounded-lg bg-white text-sm focus:outline-none focus:ring-2 focus:ring-tesla-blue"
          >
            {item.options?.map((option) => (
              <option key={option} value={option}>
                {option.charAt(0).toUpperCase() + option.slice(1)}
              </option>
            ))}
          </select>
        );

      default:
        return null;
    }
  };

  return (
    <div className="h-full bg-gray-50 p-6 overflow-y-auto">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-semibold text-tesla-darkGray mb-2">Vehicle Settings</h1>
          <p className="text-gray-600">Configure your Tesla's features and preferences</p>
        </div>

        {/* Settings Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {settingSections.map((section) => (
            <div key={section.title} className="card-tesla">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-tesla-blue/10 rounded-xl flex items-center justify-center">
                  <section.icon className="w-6 h-6 text-tesla-blue" />
                </div>
                <h2 className="text-xl font-semibold text-tesla-darkGray">{section.title}</h2>
              </div>

              <div className="space-y-4">
                {section.items.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-4 rounded-xl hover:bg-gray-50 transition-colors">
                    <div className="flex items-center space-x-3 flex-1">
                      <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                        <item.icon className="w-5 h-5 text-gray-600" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-tesla-darkGray">{item.name}</div>
                        <div className="text-sm text-gray-600">{item.description}</div>
                      </div>
                    </div>
                    <div className="ml-4">
                      {renderSettingControl(item)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* System Information */}
        <div className="card-tesla">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center">
              <InfoIcon className="w-6 h-6 text-gray-600" />
            </div>
            <h2 className="text-xl font-semibold text-tesla-darkGray">System Information</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {systemInfo.map((info, index) => (
              <div key={index} className="flex items-center justify-between p-4 rounded-xl bg-gray-50">
                <span className="text-gray-600">{info.label}:</span>
                <span className="font-medium text-tesla-darkGray">{info.value}</span>
              </div>
            ))}
          </div>

          <div className="mt-6 flex items-center justify-center space-x-4">
            <button className="px-6 py-3 bg-tesla-blue text-white rounded-xl hover:bg-tesla-darkBlue transition-colors flex items-center space-x-2">
              <UpdateIcon className="w-5 h-5" />
              <span>Check for Updates</span>
            </button>
            <button className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors flex items-center space-x-2">
              <InfoIcon className="w-5 h-5" />
              <span>System Diagnostics</span>
            </button>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="card-tesla text-center p-6 hover:bg-gray-50 transition-colors group">
            <DoorsIcon className="w-8 h-8 text-blue-500 mx-auto mb-3 group-hover:scale-110 transition-transform" />
            <div className="font-medium">Lock Doors</div>
            <div className="text-sm text-gray-600">Secure vehicle</div>
          </button>

          <button className="card-tesla text-center p-6 hover:bg-gray-50 transition-colors group">
            <TrunkIcon className="w-8 h-8 text-green-500 mx-auto mb-3 group-hover:scale-110 transition-transform" />
            <div className="font-medium">Open Trunk</div>
            <div className="text-sm text-gray-600">Rear storage</div>
          </button>

          <button className="card-tesla text-center p-6 hover:bg-gray-50 transition-colors group">
            <LightsIcon className="w-8 h-8 text-yellow-500 mx-auto mb-3 group-hover:scale-110 transition-transform" />
            <div className="font-medium">Flash Lights</div>
            <div className="text-sm text-gray-600">Locate vehicle</div>
          </button>

          <button className="card-tesla text-center p-6 hover:bg-gray-50 transition-colors group">
            <SoundIcon className="w-8 h-8 text-purple-500 mx-auto mb-3 group-hover:scale-110 transition-transform" />
            <div className="font-medium">Honk Horn</div>
            <div className="text-sm text-gray-600">Alert sound</div>
          </button>
        </div>
      </div>
    </div>
  );
}; 