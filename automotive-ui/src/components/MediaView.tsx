import React, { useState, useEffect } from 'react';

// Custom SVG Icons
const PlayIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M8 5v14l11-7z"/>
  </svg>
);

const PauseIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
  </svg>
);

const SkipPreviousIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"/>
  </svg>
);

const SkipNextIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
  </svg>
);

const VolumeIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
  </svg>
);

const AlbumIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 14.5c-2.49 0-4.5-2.01-4.5-4.5S9.51 7.5 12 7.5s4.5 2.01 4.5 4.5-2.01 4.5-4.5 4.5zm0-5.5c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1z"/>
  </svg>
);

const MusicIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
  </svg>
);

const PlaylistIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M15 6H3v2h12V6zm0 4H3v2h12v-2zM3 16h8v-2H3v2zM17 6v8.18c-.31-.11-.65-.18-1-.18-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3V8h3V6h-5z"/>
  </svg>
);

const SpotifyIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm4.589 14.427c-.239.389-.747.51-1.135.272-3.108-1.896-7.017-2.325-11.623-1.273-.434.1-.874-.178-.973-.612-.1-.434.178-.874.612-.973 5.062-1.156 9.394-.65 12.8 1.47.388.238.51.746.272 1.136zm1.61-3.58c-.297.478-.927.628-1.405.33-3.557-2.182-8.985-2.817-13.2-1.543-.515.155-1.06-.138-1.215-.653-.155-.515.138-1.06.653-1.215 4.83-1.461 10.838-.738 14.837 1.776.478.297.628.927.33 1.405z"/>
  </svg>
);

const AppleMusicIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M16.365 8.573c-.175-.26-.685-.26-.86 0-.175.26-.175.687 0 .947.175.26.685.26.86 0 .175-.26.175-.687 0-.947zm1.5-2.6c-.52-.78-2.055-.78-2.575 0-.52.78-.52 2.047 0 2.827.52.78 2.055.78 2.575 0 .52-.78.52-2.047 0-2.827zM15.75 11c-1.381 0-2.5 1.119-2.5 2.5v.5h-.5c-1.381 0-2.5 1.119-2.5 2.5s1.119 2.5 2.5 2.5h3c1.381 0 2.5-1.119 2.5-2.5v-3c0-1.381-1.119-2.5-2.5-2.5z"/>
  </svg>
);

interface Track {
  id: number;
  title: string;
  artist: string;
  album: string;
  duration: number;
  albumArt: string;
}

export const MediaView: React.FC = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(125);
  const [volume, setVolume] = useState(75);
  const [currentTrack] = useState<Track>({
    id: 1,
    title: "Electric Dreams",
    artist: "Tesla Sound System",
    album: "Future Vibes",
    duration: 248,
    albumArt: "🎵"
  });

  const recentTracks = [
    { id: 2, title: "Autopilot", artist: "Drive Mode", album: "Highway" },
    { id: 3, title: "Supercharge", artist: "Power Flow", album: "Energy" },
    { id: 4, title: "Ludicrous", artist: "Speed Demon", album: "Performance" },
    { id: 5, title: "Silent Mode", artist: "Quiet Storm", album: "Stealth" }
  ];

  const audioSources = [
    { name: "Bluetooth", icon: "📶", active: true },
    { name: "USB", icon: "💾", active: false },
    { name: "Streaming", icon: "📡", active: false },
    { name: "FM Radio", icon: "📻", active: false }
  ];

  // Simulate playback progress
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isPlaying) {
      interval = setInterval(() => {
        setCurrentTime(prev => {
          if (prev >= currentTrack.duration) {
            setIsPlaying(false);
            return 0;
          }
          return prev + 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isPlaying, currentTrack.duration]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const newTime = (clickX / rect.width) * currentTrack.duration;
    setCurrentTime(Math.floor(newTime));
  };

  return (
    <div className="h-full flex bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white">
      {/* Main player area */}
      <div className="flex-1 flex flex-col items-center justify-center p-8">
        {/* Album art */}
        <div className="w-80 h-80 rounded-3xl bg-gradient-to-br from-tesla-blue to-tesla-darkBlue flex items-center justify-center text-9xl mb-8 shadow-2xl">
          {currentTrack.albumArt}
        </div>

        {/* Track info */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">{currentTrack.title}</h1>
          <h2 className="text-xl text-gray-300 mb-1">{currentTrack.artist}</h2>
          <h3 className="text-lg text-gray-400">{currentTrack.album}</h3>
        </div>

        {/* Progress bar */}
        <div className="w-full max-w-md mb-6">
          <div
            className="progress-tesla cursor-pointer"
            onClick={handleProgressClick}
          >
            <div 
              className="progress-tesla-fill"
              style={{ width: `${(currentTime / currentTrack.duration) * 100}%` }}
            />
          </div>
          <div className="flex justify-between text-sm text-gray-400 mt-2">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(currentTrack.duration)}</span>
          </div>
        </div>

        {/* Playback controls */}
        <div className="flex items-center space-x-6 mb-8">
          <button className="w-12 h-12 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-all">
            <span className="text-xl">⏮️</span>
          </button>
          <button 
            onClick={() => setIsPlaying(!isPlaying)}
            className="w-16 h-16 rounded-full bg-tesla-blue hover:bg-tesla-darkBlue flex items-center justify-center transition-all active:scale-95 shadow-lg"
          >
            <span className="text-2xl">{isPlaying ? '⏸️' : '▶️'}</span>
          </button>
          <button className="w-12 h-12 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-all">
            <span className="text-xl">⏭️</span>
          </button>
        </div>

        {/* Volume control */}
        <div className="flex items-center space-x-4 w-full max-w-md">
          <span className="text-xl">🔊</span>
          <div className="flex-1">
            <input
              type="range"
              min="0"
              max="100"
              value={volume}
              onChange={(e) => setVolume(Number(e.target.value))}
              className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
              style={{
                background: `linear-gradient(to right, #3B82F6 0%, #3B82F6 ${volume}%, #4B5563 ${volume}%, #4B5563 100%)`
              }}
            />
          </div>
          <span className="text-sm text-gray-400 w-8">{volume}%</span>
        </div>
      </div>

      {/* Right sidebar */}
      <div className="w-80 bg-black/50 backdrop-blur border-l border-gray-700 p-6">
        {/* Audio sources */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-4">Audio Source</h3>
          <div className="grid grid-cols-2 gap-2">
            {audioSources.map((source, index) => (
              <button
                key={index}
                className={`p-3 rounded-xl border transition-all ${
                  source.active 
                    ? 'border-tesla-blue bg-tesla-blue/20 text-tesla-blue' 
                    : 'border-gray-600 hover:border-gray-500'
                }`}
              >
                <div className="text-lg mb-1">{source.icon}</div>
                <div className="text-xs">{source.name}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Recent tracks */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Recent Tracks</h3>
          <div className="space-y-2">
            {recentTracks.map((track) => (
              <button
                key={track.id}
                className="w-full p-3 rounded-xl hover:bg-white/10 transition-all text-left"
              >
                <div className="font-medium text-sm">{track.title}</div>
                <div className="text-xs text-gray-400">{track.artist}</div>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}; 