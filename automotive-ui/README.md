# 🚗 Turbo Automotive Infotainment System

A modern, sleek automotive infotainment system built with <PERSON><PERSON>, <PERSON>act, and TypeScript. Designed specifically for 15.6" touchscreen displays with a Tesla-inspired interface optimized for high-performance vehicles.

![Turbo Dashboard](https://img.shields.io/badge/Status-Active%20Development-green)
![Platform](https://img.shields.io/badge/Platform-Desktop-blue)
![License](https://img.shields.io/badge/License-MIT-blue)

## ✨ Features

### 🎛️ **Core Functionality**
- **Real-time Vehicle Monitoring**: Starter & auxiliary battery voltage monitoring
- **Fuel Management**: Live fuel level, range calculation, and cost estimation
- **Engine Diagnostics**: Temperature monitoring and performance metrics
- **3D Vehicle Visualization**: Interactive car model with door controls and lighting

### 🗺️ **Navigation System**
- **GPS Integration**: Real-time location tracking with strong signal indication
- **Route Planning**: Intelligent route optimization with traffic updates
- **Touch-Optimized Interface**: Large touch targets designed for vehicle use
- **Live Traffic**: Real-time traffic data integration

### 🎵 **Media Center**
- Modern media player interface
- Album art and track information display
- Playback controls optimized for touch interaction

### 🌡️ **Climate Control**
- Dual-zone temperature management
- Fan speed controls
- Heated seat controls
- Auto mode and recirculation

### ⚙️ **Vehicle Settings**
- Autopilot configuration
- Charging limits and preferences
- Security and lock controls
- System diagnostics and updates

### 🎨 **Design Excellence**
- **Modern Glass Morphism**: Subtle transparency effects and backdrop blur
- **Tesla-Inspired Aesthetics**: Clean, minimalist design language
- **Touch-First UI**: 44px+ touch targets throughout the interface
- **Smooth Animations**: Fluid transitions and hover effects
- **Responsive Layout**: Optimized for 15.6" touchscreen displays

## 🛠️ Technology Stack

### Frontend
- **React 18** - Modern React with hooks and TypeScript
- **TypeScript** - Type-safe JavaScript development
- **Tailwind CSS 4** - Utility-first CSS framework with custom design tokens
- **Custom SVG Icons** - Lightweight, scalable vector graphics

### Backend
- **Tauri** - Rust-based desktop application framework
- **Rust** - Systems programming language for performance

### Development Tools
- **Vite** - Fast build tool and development server
- **Node.js** - JavaScript runtime for development tools
- **Cargo** - Rust package manager

## 🚀 Quick Start

### Prerequisites
- **Node.js** (v18 or higher)
- **Rust** (latest stable version)
- **Git**

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd automotive-ui
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run tauri dev
   ```

The application will launch in a native window at `http://localhost:1420`

## 📋 Available Scripts

| Command | Description |
|---------|-------------|
| `npm run tauri dev` | Start development server with hot reload |
| `npm run tauri build` | Build production application |
| `npm run dev` | Start Vite development server only |
| `npm run build` | Build frontend for production |
| `npm run preview` | Preview production build |

## 📁 Project Structure

```
automotive-ui/
├── src/                          # Frontend source code
│   ├── components/               # React components
│   │   ├── VehicleStatusBar.tsx  # Top status bar with vehicle metrics
│   │   ├── BottomDock.tsx        # Navigation dock
│   │   ├── NavigationView.tsx    # GPS and routing interface
│   │   ├── MediaView.tsx         # Music and entertainment
│   │   ├── ClimateView.tsx       # HVAC controls
│   │   ├── SettingsView.tsx      # Vehicle settings
│   │   └── CarModel3D.tsx        # 3D car visualization
│   ├── App.tsx                   # Main application component
│   ├── App.css                   # Global styles and Tailwind config
│   └── main.tsx                  # Application entry point
├── src-tauri/                    # Tauri backend (Rust)
│   ├── src/                      # Rust source code
│   ├── Cargo.toml               # Rust dependencies
│   └── tauri.conf.json          # Tauri configuration
├── public/                       # Static assets
├── package.json                  # Node.js dependencies and scripts
├── vite.config.ts               # Vite configuration
├── tailwind.config.js           # Tailwind CSS configuration
└── README.md                    # This file
```

## ⚙️ Configuration

### Tailwind CSS 4 Setup
The project uses Tailwind CSS 4 with custom design tokens:

```css
@theme {
  --color-tesla-blue: #3B82F6;
  --color-tesla-darkBlue: #1E40AF;
  --color-tesla-gray: #F3F4F6;
  --color-tesla-darkGray: #374151;
  --color-tesla-black: #111827;
  --font-tesla: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}
```

### Tauri Configuration
Key Tauri settings in `src-tauri/tauri.conf.json`:
- Window dimensions optimized for automotive displays
- Security settings for vehicle integration
- File system access for media libraries

## 🎨 Design System

### Color Palette
- **Primary Blue**: `#3B82F6` - Main accent color
- **Dark Blue**: `#1E40AF` - Hover states and emphasis
- **Gray Scale**: `#F3F4F6` to `#111827` - Text and backgrounds
- **Status Colors**: Green (good), Yellow (warning), Red (alert)

### Typography
- **Font Family**: System fonts optimized for automotive displays
- **Font Sizes**: Scaled for 15.6" touchscreen readability
- **Font Weights**: Light to bold for proper hierarchy

### Touch Targets
- **Minimum Size**: 44px x 44px (Apple guidelines)
- **Preferred Size**: 60px+ for primary actions
- **Spacing**: 8px minimum between interactive elements

## 🚗 Vehicle Integration

### Battery Monitoring
- **Starter Battery**: 12V system monitoring (11.8V - 14.4V range)
- **Auxiliary Battery**: 12V auxiliary system (10.5V - 13.8V range)
- **Color Coding**: Green (good), Yellow (warning), Red (critical)

### Fuel System
- **Fuel Level**: Percentage-based with range calculation
- **Fuel Cost**: Real-time cost estimation based on current fuel prices
- **Range Calculation**: Dynamic range based on driving patterns

### Engine Diagnostics
- **Temperature Monitoring**: Real-time engine temperature (160°F - 220°F)
- **Performance Metrics**: Speed, RPM, and efficiency indicators
- **Alert System**: Visual warnings for critical conditions

## 🔧 Development

### Hot Reload
The development server supports hot reload for both frontend and backend changes:
- Frontend changes reload instantly
- Rust backend changes trigger automatic recompilation

### Adding New Components
1. Create component in `src/components/`
2. Add TypeScript interfaces for props
3. Include custom SVG icons if needed
4. Add to main app routing in `App.tsx`

### Styling Guidelines
- Use Tailwind utility classes
- Prefer custom design tokens from the theme
- Ensure touch targets meet size requirements
- Test on various screen sizes

## 📱 Responsive Design

The interface is optimized for:
- **Primary**: 15.6" touchscreen (1920x1080)
- **Secondary**: Various automotive display sizes
- **Touch-First**: All interactions designed for finger input

## 🔒 Security

- **File System**: Limited access to required directories only
- **Network**: Secure HTTPS connections for external data
- **User Data**: No sensitive data stored locally
- **Vehicle Integration**: Secure CAN bus communication protocols

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Standards
- **TypeScript**: Strict mode enabled
- **ESLint**: Follow configured rules
- **Prettier**: Automatic code formatting
- **Testing**: Unit tests for critical components

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙋‍♂️ Support

For support and questions:
- Open an issue on GitHub
- Check the documentation in the `/docs` folder
- Review the Tauri documentation for platform-specific questions

## 🎯 Roadmap

### Phase 1 (Current)
- ✅ Core vehicle monitoring
- ✅ 3D car visualization
- ✅ Touch-optimized navigation
- ✅ Modern UI design

### Phase 2 (Planned)
- 🔄 Real GPS integration
- 🔄 OBD-II port connectivity
- 🔄 Voice commands
- 🔄 Multi-language support

### Phase 3 (Future)
- 📋 Over-the-air updates
- 📋 Cloud synchronization
- 📋 Advanced diagnostics
- 📋 Third-party app integration

---

Built with ❤️ for modern automotive experiences
